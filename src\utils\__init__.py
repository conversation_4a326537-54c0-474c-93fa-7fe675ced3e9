# -*- coding: utf-8 -*-
"""
Utils Module / 工具模块

This module provides utility functions and classes for the GRAG system.
该模块为GRAG系统提供实用函数和类。
"""

from .config import (
    ConfigManager,
    ModelConfig,
    TrainingConfig,
    DataConfig,
    RetrievalConfig,
    WebSearchConfig,
    FrontendConfig,
    LoggingConfig,
    EnvironmentConfig,
    get_config_manager
)

# Create global config manager instance (lazy initialization)
try:
    config_manager = get_config_manager()
except Exception:
    # Fallback for import-time errors
    config_manager = None

from .logger import (
    LoggerManager,
    logger_manager,
    get_logger,
    log
)

from .helpers import (
    set_seed,
    get_device,
    clean_text,
    segment_chinese_text,
    calculate_text_similarity,
    extract_medical_entities,
    format_medical_response,
    validate_medical_safety,
    save_json,
    load_json,
    truncate_text,
    batch_process,
    efficient_tokenize
)

__all__ = [
    # Config
    "ConfigManager",
    "ModelConfig",
    "TrainingConfig",
    "DataConfig",
    "RetrievalConfig",
    "WebSearchConfig",
    "FrontendConfig",
    "LoggingConfig",
    "EnvironmentConfig",
    "get_config_manager",
    "config_manager",
    
    # Logger
    "LoggerManager",
    "logger_manager", 
    "get_logger",
    "log",
    
    # Helpers
    "set_seed",
    "get_device",
    "clean_text",
    "segment_chinese_text",
    "calculate_text_similarity",
    "extract_medical_entities",
    "format_medical_response",
    "validate_medical_safety",
    "save_json",
    "load_json",
    "truncate_text",
    "batch_process",
    "efficient_tokenize"
]
