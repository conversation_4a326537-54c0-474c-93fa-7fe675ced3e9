#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Warnings Filter / 测试警告过滤器

This script tests the warning management system to ensure pkg_resources warnings are suppressed.
该脚本测试警告管理系统以确保pkg_resources警告被抑制。
"""

import sys
import os
from pathlib import Path

# Add src to path / 添加src到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def test_warnings_before_filter():
    """Test warnings before applying filter / 应用过滤器前测试警告"""
    print("🧪 Testing warnings BEFORE filter...")
    print("=" * 50)
    
    try:
        # This should show the pkg_resources warning
        import jieba
        result = list(jieba.cut("测试中文分词"))
        print(f"✅ Jieba import successful: {result}")
    except Exception as e:
        print(f"❌ Jieba import failed: {e}")
    
    print("=" * 50)
    print()

def test_warnings_after_filter():
    """Test warnings after applying filter / 应用过滤器后测试警告"""
    print("🔧 Testing warnings AFTER filter...")
    print("=" * 50)
    
    try:
        # Import utils (which should initialize warning management)
        from utils import show_warning_summary, create_warning_context
        
        # Show warning management status
        show_warning_summary()
        print()
        
        # Test jieba again (should not show warnings now)
        print("📖 Testing jieba with warning filter...")
        with create_warning_context():
            import jieba
            result = list(jieba.cut("测试中文分词功能"))
            print(f"✅ Jieba test successful (no warnings): {result}")
        
    except Exception as e:
        print(f"❌ Warning filter test failed: {e}")
    
    print("=" * 50)
    print()

def test_environment_variables():
    """Test environment variables setup / 测试环境变量设置"""
    print("🌍 Testing environment variables...")
    print("=" * 50)
    
    env_vars_to_check = [
        "TOKENIZERS_PARALLELISM",
        "HF_HOME",
        "WANDB_DISABLED"
    ]
    
    for var in env_vars_to_check:
        value = os.environ.get(var, "Not set")
        status = "✅" if value != "Not set" else "⚠️"
        print(f"{status} {var}: {value}")
    
    print("=" * 50)
    print()

def test_specific_imports():
    """Test specific imports that commonly cause warnings / 测试通常引起警告的特定导入"""
    print("📦 Testing specific imports...")
    print("=" * 50)
    
    imports_to_test = [
        ("jieba", "jieba"),
        ("opencc", "opencc"),
        ("transformers", "transformers"),
        ("datasets", "datasets"),
    ]
    
    for import_name, module_name in imports_to_test:
        try:
            print(f"🔄 Testing {import_name}...")
            __import__(module_name)
            print(f"✅ {import_name} imported successfully")
        except ImportError as e:
            print(f"⚠️ {import_name} not available: {e}")
        except Exception as e:
            print(f"❌ {import_name} import error: {e}")
    
    print("=" * 50)
    print()

def test_warning_context_manager():
    """Test the warning context manager / 测试警告上下文管理器"""
    print("🎭 Testing warning context manager...")
    print("=" * 50)
    
    try:
        from utils import create_warning_context
        
        print("📝 Testing context manager...")
        with create_warning_context():
            # Code that might generate warnings
            import warnings
            warnings.warn("This is a test warning", UserWarning)
            print("✅ Context manager test completed")
        
    except Exception as e:
        print(f"❌ Context manager test failed: {e}")
    
    print("=" * 50)
    print()

def main():
    """Main test function / 主测试函数"""
    print("🧪 GRAG Warning Management Test Suite")
    print("🧪 GRAG警告管理测试套件")
    print("=" * 60)
    print()
    
    # Test 1: Environment variables
    test_environment_variables()
    
    # Test 2: Warnings after filter (utils import initializes filter)
    test_warnings_after_filter()
    
    # Test 3: Specific imports
    test_specific_imports()
    
    # Test 4: Context manager
    test_warning_context_manager()
    
    # Final summary
    print("🎉 Warning Management Test Summary")
    print("=" * 60)
    print("✅ Environment variables configured")
    print("✅ Warning filters applied")
    print("✅ Specific imports tested")
    print("✅ Context manager tested")
    print()
    print("💡 Tips / 提示:")
    print("   - pkg_resources warnings should be suppressed")
    print("   - Environment variables should be set automatically")
    print("   - Use create_warning_context() for temporary warning suppression")
    print("=" * 60)

if __name__ == "__main__":
    main()
