# -*- coding: utf-8 -*-
"""
Warnings Filter Module / 警告过滤模块

This module provides centralized warning management for the GRAG system.
该模块为GRAG系统提供集中的警告管理。
"""

import warnings
import os
import sys
from typing import List, Dict, Any


def setup_warning_filters():
    """
    Setup warning filters to suppress known non-critical warnings
    设置警告过滤器以抑制已知的非关键警告
    """
    
    # Filter pkg_resources deprecation warnings from jieba
    # 过滤来自jieba的pkg_resources弃用警告
    warnings.filterwarnings(
        "ignore",
        message="pkg_resources is deprecated as an API.*",
        category=UserWarning,
        module="jieba.*"
    )
    
    # Filter setuptools warnings
    # 过滤setuptools警告
    warnings.filterwarnings(
        "ignore",
        message=".*pkg_resources.*",
        category=UserWarning
    )
    
    # Filter wandb deprecation warnings
    # 过滤wandb弃用警告
    warnings.filterwarnings(
        "ignore",
        message=".*Using the `WANDB_DISABLED` environment variable is deprecated.*",
        category=UserWarning
    )

    # Filter transformers warnings about fast tokenizers
    # 过滤transformers关于快速分词器的警告
    warnings.filterwarnings(
        "ignore",
        message=".*Using the `WANDB_DISABLED` environment variable.*",
        category=UserWarning
    )
    
    # Filter unsloth warnings if not using GPU
    # 如果不使用GPU则过滤unsloth警告
    warnings.filterwarnings(
        "ignore",
        message=".*Unsloth should be imported before.*",
        category=UserWarning
    )
    
    # Filter FAISS warnings about GPU
    # 过滤FAISS关于GPU的警告
    warnings.filterwarnings(
        "ignore",
        message=".*FAISS GPU support not available.*",
        category=UserWarning
    )


def setup_environment_variables():
    """
    Setup environment variables to reduce warnings
    设置环境变量以减少警告
    """
    
    # Disable tokenizers parallelism warning
    # 禁用tokenizers并行处理警告
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    
    # Set HuggingFace cache directory
    # 设置HuggingFace缓存目录
    if "HF_HOME" not in os.environ:
        os.environ["HF_HOME"] = os.path.expanduser("~/.cache/huggingface")

    # Note: WANDB_DISABLED is deprecated, use --report_to none instead
    # 注意：WANDB_DISABLED已弃用，请使用--report_to none代替
    # We no longer set WANDB_DISABLED to avoid deprecation warnings
    # 我们不再设置WANDB_DISABLED以避免弃用警告


def suppress_specific_warnings():
    """
    Suppress specific warnings that are known to be safe to ignore
    抑制已知可以安全忽略的特定警告
    """
    
    # List of warning patterns to suppress
    # 要抑制的警告模式列表
    warning_patterns = [
        # pkg_resources deprecation
        "pkg_resources is deprecated as an API",
        "The pkg_resources package is slated for removal",
        
        # Setuptools warnings
        "Setuptools is replacing distutils",
        
        # Wandb warnings
        "Using the `WANDB_DISABLED` environment variable is deprecated",
        "Using the `WANDB_DISABLED` environment variable",

        # Transformers warnings
        "The attention mask and the pad token id were not set",
        
        # FAISS warnings
        "FAISS GPU support not available",
        
        # Unsloth warnings
        "Unsloth should be imported before",
        "WARNING: Unsloth should be imported",
        
        # Other common warnings
        "FutureWarning: The behavior of DataFrame concatenation",
        "DeprecationWarning: np.find_common_type is deprecated",
    ]
    
    for pattern in warning_patterns:
        warnings.filterwarnings("ignore", message=f".*{pattern}.*")


def configure_logging_warnings():
    """
    Configure logging to handle warnings appropriately
    配置日志记录以适当处理警告
    """
    
    # Capture warnings in logging
    # 在日志记录中捕获警告
    import logging
    logging.captureWarnings(True)
    
    # Set warning logger level
    # 设置警告日志记录器级别
    warnings_logger = logging.getLogger("py.warnings")
    warnings_logger.setLevel(logging.ERROR)  # Only show error-level warnings


def initialize_warning_management():
    """
    Initialize comprehensive warning management
    初始化综合警告管理
    
    This function should be called at the start of the application
    此函数应在应用程序启动时调用
    """
    
    # Setup environment variables first
    # 首先设置环境变量
    setup_environment_variables()
    
    # Setup warning filters
    # 设置警告过滤器
    setup_warning_filters()
    
    # Suppress specific warnings
    # 抑制特定警告
    suppress_specific_warnings()
    
    # Configure logging for warnings
    # 为警告配置日志记录
    configure_logging_warnings()


def create_warning_context():
    """
    Create a context manager for temporarily suppressing warnings
    创建用于临时抑制警告的上下文管理器
    
    Usage:
        with create_warning_context():
            # Code that might generate warnings
            import jieba
            jieba.cut("测试文本")
    """
    
    class WarningContext:
        def __enter__(self):
            self.old_filters = warnings.filters[:]
            suppress_specific_warnings()
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            warnings.filters[:] = self.old_filters
    
    return WarningContext()


def show_warning_summary():
    """
    Show a summary of warning management status
    显示警告管理状态摘要
    """
    
    print("🔧 Warning Management Status / 警告管理状态")
    print("=" * 50)
    
    # Check environment variables
    env_vars = [
        "TOKENIZERS_PARALLELISM",
        "HF_HOME"
    ]
    
    for var in env_vars:
        value = os.environ.get(var, "Not set")
        print(f"📝 {var}: {value}")
    
    # Check active warning filters
    print(f"⚠️  Active warning filters: {len(warnings.filters)}")
    
    print("=" * 50)


# Auto-initialize when module is imported
# 模块导入时自动初始化
if __name__ != "__main__":
    initialize_warning_management()


if __name__ == "__main__":
    # Test the warning management
    # 测试警告管理
    print("🧪 Testing Warning Management / 测试警告管理")
    
    # Show current status
    show_warning_summary()
    
    # Test jieba import (should not show pkg_resources warning)
    print("\n📖 Testing jieba import...")
    try:
        import jieba
        result = list(jieba.cut("测试中文分词功能"))
        print(f"✅ Jieba test successful: {result}")
    except Exception as e:
        print(f"❌ Jieba test failed: {e}")
    
    print("\n✅ Warning management test completed!")
