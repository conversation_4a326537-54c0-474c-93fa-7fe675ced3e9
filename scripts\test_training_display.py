#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Training Display Features / 测试训练显示功能

This script tests the enhanced progress and debug information display for training scripts.
该脚本测试训练脚本的增强进度和调试信息显示。
"""

import sys
import os
from pathlib import Path

# Add src to path / 添加src到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
    from utils import get_logger, config_manager
    
    console = Console()
    logger = get_logger(__name__)
    
    def test_sft_display():
        """Test SFT training display / 测试SFT训练显示"""
        console.print(Panel.fit("🏥 Testing SFT Training Display / 测试SFT训练显示", style="bold green"))
        
        # Mock configuration / 模拟配置
        class MockConfig:
            def __init__(self):
                self.model = type('obj', (object,), {'base_model': 'Qwen/Qwen3-8B'})
                self.training = type('obj', (object,), {
                    'sft': {
                        'num_train_epochs': 3,
                        'learning_rate': 2e-4,
                        'per_device_train_batch_size': 2,
                        'gradient_accumulation_steps': 8,
                        'output_dir': './models/sft'
                    }
                })
        
        mock_config = MockConfig()
        
        # Create training info table / 创建训练信息表格
        table = Table(title="🚀 SFT Training Configuration / SFT训练配置", show_header=True, header_style="bold magenta")
        table.add_column("配置项", style="cyan", no_wrap=True)
        table.add_column("值", style="green")
        
        # Add configuration rows / 添加配置行
        table.add_row("基础模型", mock_config.model.base_model)
        table.add_row("训练轮数", str(mock_config.training.sft["num_train_epochs"]))
        table.add_row("学习率", str(mock_config.training.sft["learning_rate"]))
        table.add_row("批次大小", str(mock_config.training.sft["per_device_train_batch_size"]))
        table.add_row("梯度累积步数", str(mock_config.training.sft["gradient_accumulation_steps"]))
        table.add_row("训练样本数", "1000")
        table.add_row("验证样本数", "200")
        table.add_row("输出目录", mock_config.training.sft["output_dir"])
        
        console.print(table)
        console.print()
        
        # Test progress display / 测试进度显示
        console.print("📊 Step 100/1000 | Loss: 0.5432 | LR: 2.00e-04", style="bold blue")
        console.print()
    
    def test_grpo_display():
        """Test GRPO training display / 测试GRPO训练显示"""
        console.print(Panel.fit("🎯 Testing GRPO Training Display / 测试GRPO训练显示", style="bold blue"))
        
        # Create GRPO training info table / 创建GRPO训练信息表格
        table = Table(title="🎯 GRPO Training Configuration / GRPO训练配置", show_header=True, header_style="bold magenta")
        table.add_column("配置项", style="cyan", no_wrap=True)
        table.add_column("值", style="green")
        
        # Add configuration rows / 添加配置行
        table.add_row("基础模型", "Qwen/Qwen3-8B")
        table.add_row("SFT模型路径", "./models/sft")
        table.add_row("训练轮数", "1")
        table.add_row("学习率", "5e-6")
        table.add_row("批次大小", "1")
        table.add_row("生成数量", "4")
        table.add_row("奖励函数数量", "5")
        table.add_row("训练样本数", "800")
        table.add_row("验证样本数", "200")
        table.add_row("输出目录", "./models/grpo")
        
        console.print(table)
        console.print()
        
        # Test reward functions display / 测试奖励函数显示
        reward_table = Table(title="🏆 Reward Functions / 奖励函数", show_header=True, header_style="bold blue")
        reward_table.add_column("奖励函数", style="cyan")
        reward_table.add_column("类型", style="yellow")
        reward_table.add_column("权重", style="green")
        
        reward_table.add_row("medical_accuracy", "LLM-based", "0.35")
        reward_table.add_row("relevance", "Embedding-based", "0.25")
        reward_table.add_row("safety", "Rule-based", "0.25")
        reward_table.add_row("completeness", "Length-based", "0.10")
        reward_table.add_row("format_compliance", "Regex-based", "0.05")
        
        console.print(reward_table)
        console.print()
    
    def test_progress_bar():
        """Test progress bar / 测试进度条"""
        console.print(Panel.fit("📊 Testing Progress Bar / 测试进度条", style="bold yellow"))
        
        import time
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=console
        ) as progress:
            
            task = progress.add_task("Training model...", total=100)
            
            for i in range(100):
                time.sleep(0.02)  # Simulate work
                progress.update(task, advance=1)
                
                if i % 20 == 0:
                    progress.console.print(f"📊 Epoch {i//20 + 1}/5 | Loss: {0.8 - i*0.005:.4f}", style="bold blue")
        
        console.print()
    
    def main():
        """Main test function / 主测试函数"""
        console.print(Panel.fit("🧪 Training Display Test Suite / 训练显示测试套件", style="bold white on blue"))
        console.print()
        
        try:
            # Test SFT display / 测试SFT显示
            test_sft_display()
            
            # Test GRPO display / 测试GRPO显示
            test_grpo_display()
            
            # Test progress bar / 测试进度条
            test_progress_bar()
            
            # Success message / 成功消息
            console.print(Panel.fit("✅ All display tests passed! / 所有显示测试通过！", style="bold green"))
            
        except Exception as e:
            console.print(f"❌ Test failed: {e}", style="bold red")
            logger.error(f"Display test failed: {e}")
            return False
        
        return True

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Please install required packages:")
    print("   pip install rich tqdm")
    sys.exit(1)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
