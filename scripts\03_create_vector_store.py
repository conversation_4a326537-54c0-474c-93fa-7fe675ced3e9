#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vector Store Creation Script / 向量存储创建脚本

This script creates the FAISS vector store from the knowledge base with detailed progress tracking.
该脚本从知识库创建FAISS向量存储，并提供详细的进度跟踪。
"""

import sys
import os
import gc
import time
import json
from pathlib import Path
from typing import List, Dict, Any

# Add project root and src to path
project_root = Path(__file__).parent.parent
src_path = project_root / "src"

# Change to project root directory
os.chdir(project_root)

# Add both paths to sys.path
for path in [str(project_root), str(src_path)]:
    if path not in sys.path:
        sys.path.insert(0, path)

# Set PYTHONPATH environment variable
os.environ['PYTHONPATH'] = str(src_path) + os.pathsep + os.environ.get('PYTHONPATH', '')

def import_modules():
    """Import required modules with better error handling"""
    try:
        # Try absolute imports first
        sys.path.insert(0, str(src_path))

        from retrieval import MedicalVectorStore
        from utils import get_logger, get_config_manager

        return MedicalVectorStore, get_logger, get_config_manager

    except ImportError as e1:
        print(f"First import attempt failed: {e1}")
        try:
            # Try with src prefix
            from src.retrieval import MedicalVectorStore
            from src.utils import get_logger, get_config_manager

            return MedicalVectorStore, get_logger, get_config_manager

        except ImportError as e2:
            print(f"Second import attempt failed: {e2}")
            print("Please ensure all dependencies are installed and the project structure is correct.")
            sys.exit(1)

# Import all required modules
MedicalVectorStore, get_logger, get_config_manager = import_modules()

logger = get_logger(__name__)


def load_knowledge_base(knowledge_base_path: str) -> List[Dict[str, Any]]:
    """
    Load knowledge base from JSONL file
    从JSONL文件加载知识库
    
    Args:
        knowledge_base_path: Path to knowledge base file
        
    Returns:
        List of documents
    """
    documents = []
    logger.info(f"Loading knowledge base from {knowledge_base_path}")
    
    try:
        with open(knowledge_base_path, "r", encoding="utf-8") as f:
            for line_num, line in enumerate(f, 1):
                if line.strip():
                    try:
                        doc = json.loads(line.strip())
                        documents.append(doc)
                        
                        # Progress update every 100 documents
                        if line_num % 100 == 0:
                            print(f"📖 Loaded {line_num} documents...")
                            
                    except json.JSONDecodeError as e:
                        logger.warning(f"Skipping invalid JSON on line {line_num}: {e}")
                        
        logger.info(f"✅ Successfully loaded {len(documents)} documents from knowledge base")
        return documents
        
    except FileNotFoundError:
        logger.error(f"❌ Knowledge base file not found: {knowledge_base_path}")
        return []
    except Exception as e:
        logger.error(f"❌ Error loading knowledge base: {e}")
        return []


def create_vector_store_with_progress(
    documents: List[Dict[str, Any]],
    vector_store_path: str,
    config_manager,
    embedding_model: str = None,
    batch_size: int = None
):
    """
    Create vector store with detailed progress tracking
    创建带有详细进度跟踪的向量存储

    Args:
        documents: List of documents to process
        vector_store_path: Path to save vector store
        config_manager: Configuration manager instance
        embedding_model: Embedding model name
        batch_size: Batch size for processing (if None, read from config)

    Returns:
        Created vector store
    """
    # Get batch size from config if not provided / 如果未提供批次大小，从配置中获取
    if batch_size is None:
        batch_size = config_manager.retrieval.vector_db.get("creation_batch_size", 4)

    logger.info("🚀 Starting vector store creation with detailed progress tracking...")
    logger.info(f"📊 Total documents to process: {len(documents)}")
    logger.info(f"📦 Batch size: {batch_size} (from config)")
    logger.info(f"🎯 Target path: {vector_store_path}")
    
    # Initialize vector store
    vector_store = MedicalVectorStore(embedding_model=embedding_model)
    
    # Load embedding model first
    logger.info("🔄 Loading embedding model...")
    start_time = time.time()
    vector_store.load_embedding_model()
    load_time = time.time() - start_time
    logger.info(f"✅ Embedding model loaded in {load_time:.2f} seconds")
    
    # Process documents in batches
    total_docs = len(documents)
    total_batches = (total_docs + batch_size - 1) // batch_size
    processed = 0
    
    logger.info(f"📋 Processing {total_docs} documents in {total_batches} batches")
    logger.info("=" * 80)
    
    overall_start_time = time.time()
    
    try:
        for batch_idx in range(total_batches):
            batch_start_idx = batch_idx * batch_size
            batch_end_idx = min(batch_start_idx + batch_size, total_docs)
            batch = documents[batch_start_idx:batch_end_idx]
            
            batch_start_time = time.time()
            
            # Progress header
            progress_pct = (batch_idx / total_batches) * 100
            logger.info(f"🔄 Batch {batch_idx + 1}/{total_batches} ({progress_pct:.1f}%) - Processing {len(batch)} documents")
            
            # Extract and validate texts
            texts = []
            for doc in batch:
                text = doc.get("text", "")
                if not text:
                    # Create text from query and response if text is missing
                    query = doc.get("query", "")
                    response = doc.get("response", "")
                    text = f"问题：{query}\n回答：{response}" if query or response else "空文档"
                texts.append(text)
            
            # Generate embeddings
            logger.debug(f"  🧠 Generating embeddings for {len(texts)} texts...")
            embedding_start = time.time()
            embeddings = vector_store.encode_texts(texts)
            embedding_time = time.time() - embedding_start
            logger.debug(f"  ✅ Embeddings generated in {embedding_time:.2f}s - Shape: {embeddings.shape}")
            
            # Create index on first batch
            if vector_store.index is None:
                actual_dimension = embeddings.shape[1]
                if vector_store.dimension != actual_dimension:
                    logger.info(f"  📏 Updating dimension from {vector_store.dimension} to {actual_dimension}")
                    vector_store.dimension = actual_dimension
                
                logger.info(f"  🏗️ Creating FAISS index with dimension {actual_dimension}")
                vector_store.create_index()
                logger.info(f"  ✅ FAISS index created successfully")
            
            # Add to FAISS index
            logger.debug(f"  📥 Adding {len(embeddings)} embeddings to FAISS index...")
            index_start = time.time()
            vector_store.index.add(embeddings)
            index_time = time.time() - index_start
            logger.debug(f"  ✅ Added to index in {index_time:.2f}s")
            
            # Store documents and metadata
            vector_store.documents.extend(batch)
            vector_store.metadata.extend([doc.get("metadata", {}) for doc in batch])
            
            processed += len(batch)
            batch_time = time.time() - batch_start_time
            
            # Progress summary
            elapsed_time = time.time() - overall_start_time
            avg_time_per_batch = elapsed_time / (batch_idx + 1)
            remaining_batches = total_batches - (batch_idx + 1)
            eta = remaining_batches * avg_time_per_batch
            
            logger.info(f"  ✅ Batch completed in {batch_time:.2f}s")
            logger.info(f"  📈 Progress: {processed}/{total_docs} documents ({processed/total_docs*100:.1f}%)")
            logger.info(f"  ⏱️ Elapsed: {elapsed_time:.1f}s | ETA: {eta:.1f}s")
            
            # Memory cleanup
            del embeddings, texts
            gc.collect()
            
            # Progress bar visualization
            bar_length = 50
            filled_length = int(bar_length * processed / total_docs)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)
            logger.info(f"  📊 [{bar}] {processed}/{total_docs}")
            logger.info("-" * 80)
            
            # Small delay to prevent overwhelming the system
            time.sleep(0.1)
            
    except Exception as e:
        logger.error(f"❌ Error processing batch {batch_idx + 1}: {e}")
        raise
    
    # Save the vector store
    total_time = time.time() - overall_start_time
    logger.info("💾 Saving vector store...")
    save_start = time.time()
    vector_store.save(vector_store_path)
    save_time = time.time() - save_start
    
    # Final summary
    logger.info("=" * 80)
    logger.info("🎉 VECTOR STORE CREATION COMPLETED SUCCESSFULLY!")
    logger.info("=" * 80)
    logger.info(f"📊 Total documents processed: {len(vector_store.documents)}")
    logger.info(f"📏 Vector dimension: {vector_store.dimension}")
    logger.info(f"💾 Saved to: {vector_store_path}")
    logger.info(f"⏱️ Total processing time: {total_time:.2f} seconds")
    logger.info(f"💾 Save time: {save_time:.2f} seconds")
    logger.info(f"📈 Average time per document: {total_time/len(documents):.3f} seconds")
    logger.info("=" * 80)
    
    return vector_store


def test_vector_store(vector_store: MedicalVectorStore):
    """
    Test the created vector store
    测试创建的向量存储
    """
    logger.info("🧪 Testing vector store...")
    
    test_queries = [
        "头痛怎么办",
        "发烧如何处理", 
        "咳嗽的原因",
        "胃痛需要注意什么",
        "高血压的治疗方法"
    ]
    
    for query in test_queries:
        try:
            start_time = time.time()
            results = vector_store.search(query, top_k=3)
            search_time = time.time() - start_time
            
            logger.info(f"🔍 Query: '{query}'")
            logger.info(f"  📋 Found {len(results)} results in {search_time:.3f}s")
            
            if results:
                top_result = results[0]
                logger.info(f"  🏆 Top result score: {top_result[1]:.4f}")
                top_text = top_result[0].get('text', '')[:100]
                logger.info(f"  📝 Top result preview: {top_text}...")
            else:
                logger.warning(f"  ⚠️ No results found for query: {query}")
                
            logger.info("-" * 50)
            
        except Exception as e:
            logger.error(f"❌ Error testing query '{query}': {e}")


def main():
    """Main function / 主函数"""
    logger.info("🏥 GRAG Medical AI - Vector Store Creation")
    logger.info("=" * 80)
    
    try:
        # Get configuration
        config_manager = get_config_manager()
        vector_store_path = config_manager.retrieval.vector_db.get("save_path", "data/embeddings/faiss_index")
        knowledge_base_path = "data/processed/knowledge_base.jsonl"
        
        # Check if knowledge base exists
        if not Path(knowledge_base_path).exists():
            logger.error(f"❌ Knowledge base not found: {knowledge_base_path}")
            logger.info("Please run 'python scripts/01_download_data.py' first to create the knowledge base")
            return
        
        # Load knowledge base
        documents = load_knowledge_base(knowledge_base_path)
        if not documents:
            logger.error("❌ No documents loaded from knowledge base")
            return
        
        # Create vector store
        vector_store = create_vector_store_with_progress(
            documents=documents,
            vector_store_path=vector_store_path,
            config_manager=config_manager
        )
        
        # Test the vector store
        test_vector_store(vector_store)
        
        logger.info("🎯 NEXT STEPS:")
        logger.info("1. Run SFT training: python scripts/03_train_sft.py")
        logger.info("2. Run GRPO training: python scripts/04_train_grpo.py")
        logger.info("3. Start frontend: python scripts/05_run_app.py")
        logger.info("=" * 80)
        
    except Exception as e:
        logger.error(f"❌ Error in vector store creation: {e}")
        raise


if __name__ == "__main__":
    main()
