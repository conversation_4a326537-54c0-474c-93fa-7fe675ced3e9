#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vector Store Creation Script / 向量存储创建脚本

This script creates the FAISS vector store from the knowledge base with detailed progress tracking.
该脚本从知识库创建FAISS向量存储，并提供详细的进度跟踪。
"""

import sys
import os
import gc
import time
import json
from pathlib import Path
from typing import List, Dict, Any
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.table import Table
from rich.panel import Panel
from rich.live import Live
from rich.layout import Layout

# Add project root and src to path
project_root = Path(__file__).parent.parent
src_path = project_root / "src"

# Change to project root directory
os.chdir(project_root)

# Add both paths to sys.path
for path in [str(project_root), str(src_path)]:
    if path not in sys.path:
        sys.path.insert(0, path)

# Set PYTHONPATH environment variable
os.environ['PYTHONPATH'] = str(src_path) + os.pathsep + os.environ.get('PYTHONPATH', '')

def import_modules():
    """Import required modules with better error handling"""
    try:
        # Try absolute imports first
        sys.path.insert(0, str(src_path))

        from retrieval import MedicalVectorStore
        from utils import get_logger, get_config_manager

        return MedicalVectorStore, get_logger, get_config_manager

    except ImportError as e1:
        print(f"First import attempt failed: {e1}")
        try:
            # Try with src prefix
            from src.retrieval import MedicalVectorStore
            from src.utils import get_logger, get_config_manager

            return MedicalVectorStore, get_logger, get_config_manager

        except ImportError as e2:
            print(f"Second import attempt failed: {e2}")
            print("Please ensure all dependencies are installed and the project structure is correct.")
            sys.exit(1)

# Import all required modules
MedicalVectorStore, get_logger, get_config_manager = import_modules()

logger = get_logger(__name__)
console = Console()


def display_vector_store_config(config_manager, documents_count: int, vector_store_path: str):
    """Display vector store configuration / 显示向量存储配置"""

    # Create configuration table / 创建配置表格
    table = Table(title="🗂️ Vector Store Configuration / 向量存储配置", show_header=True, header_style="bold magenta")
    table.add_column("配置项", style="cyan", no_wrap=True)
    table.add_column("值", style="green")

    # Add configuration rows / 添加配置行
    table.add_row("嵌入模型", config_manager.retrieval.embedding.get("model_name", "Unknown"))
    table.add_row("向量维度", str(config_manager.retrieval.vector_db.get("dimension", 1024)))
    table.add_row("索引类型", config_manager.retrieval.vector_db.get("index_type", "IndexFlatIP"))
    table.add_row("批次大小", str(config_manager.retrieval.vector_db.get("creation_batch_size", 4)))
    table.add_row("文档总数", str(documents_count))
    table.add_row("保存路径", vector_store_path)
    table.add_row("GPU加速", config_manager.retrieval.vector_db.get("use_gpu", "auto"))

    console.print(table)
    console.print()


def display_batch_progress(batch_idx: int, total_batches: int, batch_size: int,
                          processed: int, total_docs: int, batch_time: float,
                          elapsed_time: float, eta: float):
    """Display batch processing progress / 显示批次处理进度"""

    # Progress percentage / 进度百分比
    progress_pct = (processed / total_docs) * 100

    # Create progress info / 创建进度信息
    progress_info = f"Batch {batch_idx + 1}/{total_batches} | {processed}/{total_docs} docs ({progress_pct:.1f}%)"

    # Time info / 时间信息
    time_info = f"Batch: {batch_time:.2f}s | Elapsed: {elapsed_time:.1f}s | ETA: {eta:.1f}s"

    # Progress bar / 进度条
    bar_length = 40
    filled_length = int(bar_length * processed / total_docs)
    bar = '█' * filled_length + '░' * (bar_length - filled_length)

    console.print(f"🔄 {progress_info}", style="bold blue")
    console.print(f"⏱️  {time_info}", style="yellow")
    console.print(f"📊 [{bar}] {progress_pct:.1f}%", style="green")
    console.print()


def load_knowledge_base(knowledge_base_path: str) -> List[Dict[str, Any]]:
    """
    Load knowledge base from JSONL file
    从JSONL文件加载知识库
    
    Args:
        knowledge_base_path: Path to knowledge base file
        
    Returns:
        List of documents
    """
    documents = []
    console.print(f"📖 Loading knowledge base from [cyan]{knowledge_base_path}[/cyan]", style="bold")
    logger.info(f"Loading knowledge base from {knowledge_base_path}")

    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=console
        ) as progress:

            # First pass: count total lines / 第一遍：计算总行数
            with open(knowledge_base_path, "r", encoding="utf-8") as f:
                total_lines = sum(1 for line in f if line.strip())

            task = progress.add_task("Loading documents...", total=total_lines)

            with open(knowledge_base_path, "r", encoding="utf-8") as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        try:
                            doc = json.loads(line.strip())
                            documents.append(doc)
                            progress.update(task, advance=1)

                        except json.JSONDecodeError as e:
                            logger.warning(f"Skipping invalid JSON on line {line_num}: {e}")
                            progress.update(task, advance=1)

        console.print(f"✅ Successfully loaded [bold green]{len(documents)}[/bold green] documents from knowledge base")
        logger.info(f"✅ Successfully loaded {len(documents)} documents from knowledge base")
        return documents
        
    except FileNotFoundError:
        logger.error(f"❌ Knowledge base file not found: {knowledge_base_path}")
        return []
    except Exception as e:
        logger.error(f"❌ Error loading knowledge base: {e}")
        return []


def create_vector_store_with_progress(
    documents: List[Dict[str, Any]],
    vector_store_path: str,
    config_manager,
    embedding_model: str = None,
    batch_size: int = None
):
    """
    Create vector store with detailed progress tracking
    创建带有详细进度跟踪的向量存储

    Args:
        documents: List of documents to process
        vector_store_path: Path to save vector store
        config_manager: Configuration manager instance
        embedding_model: Embedding model name
        batch_size: Batch size for processing (if None, read from config)

    Returns:
        Created vector store
    """
    # Get batch size from config if not provided / 如果未提供批次大小，从配置中获取
    if batch_size is None:
        batch_size = config_manager.retrieval.vector_db.get("creation_batch_size", 4)

    # Display startup banner / 显示启动横幅
    console.print(Panel.fit("🗂️ GRAG Vector Store Creation / GRAG向量存储创建", style="bold green"))

    # Display configuration / 显示配置
    display_vector_store_config(config_manager, len(documents), vector_store_path)

    logger.info("🚀 Starting vector store creation with detailed progress tracking...")
    logger.info(f"📊 Total documents to process: {len(documents)}")
    logger.info(f"📦 Batch size: {batch_size} (from config)")
    logger.info(f"🎯 Target path: {vector_store_path}")

    # Initialize vector store
    console.print("🔧 Initializing vector store...", style="bold cyan")
    vector_store = MedicalVectorStore(embedding_model=embedding_model)
    
    # Load embedding model first
    console.print("🧠 Loading embedding model...", style="bold yellow")
    logger.info("🔄 Loading embedding model...")
    start_time = time.time()
    vector_store.load_embedding_model()
    load_time = time.time() - start_time
    console.print(f"✅ Embedding model loaded in [bold green]{load_time:.2f}[/bold green] seconds")
    logger.info(f"✅ Embedding model loaded in {load_time:.2f} seconds")
    
    # Process documents in batches
    total_docs = len(documents)
    total_batches = (total_docs + batch_size - 1) // batch_size
    processed = 0
    
    console.print(f"📋 Processing [bold cyan]{total_docs}[/bold cyan] documents in [bold cyan]{total_batches}[/bold cyan] batches")
    console.print("=" * 80)
    logger.info(f"📋 Processing {total_docs} documents in {total_batches} batches")
    logger.info("=" * 80)

    overall_start_time = time.time()
    
    try:
        for batch_idx in range(total_batches):
            batch_start_idx = batch_idx * batch_size
            batch_end_idx = min(batch_start_idx + batch_size, total_docs)
            batch = documents[batch_start_idx:batch_end_idx]
            
            batch_start_time = time.time()
            
            # Progress header
            progress_pct = (batch_idx / total_batches) * 100
            logger.info(f"🔄 Batch {batch_idx + 1}/{total_batches} ({progress_pct:.1f}%) - Processing {len(batch)} documents")
            
            # Extract and validate texts
            texts = []
            for doc in batch:
                text = doc.get("text", "")
                if not text:
                    # Create text from query and response if text is missing
                    query = doc.get("query", "")
                    response = doc.get("response", "")
                    text = f"问题：{query}\n回答：{response}" if query or response else "空文档"
                texts.append(text)
            
            # Generate embeddings
            logger.debug(f"  🧠 Generating embeddings for {len(texts)} texts...")
            embedding_start = time.time()
            embeddings = vector_store.encode_texts(texts)
            embedding_time = time.time() - embedding_start
            logger.debug(f"  ✅ Embeddings generated in {embedding_time:.2f}s - Shape: {embeddings.shape}")
            
            # Create index on first batch
            if vector_store.index is None:
                actual_dimension = embeddings.shape[1]
                if vector_store.dimension != actual_dimension:
                    logger.info(f"  📏 Updating dimension from {vector_store.dimension} to {actual_dimension}")
                    vector_store.dimension = actual_dimension
                
                logger.info(f"  🏗️ Creating FAISS index with dimension {actual_dimension}")
                vector_store.create_index()
                logger.info(f"  ✅ FAISS index created successfully")
            
            # Add to FAISS index
            logger.debug(f"  📥 Adding {len(embeddings)} embeddings to FAISS index...")
            index_start = time.time()
            vector_store.index.add(embeddings)
            index_time = time.time() - index_start
            logger.debug(f"  ✅ Added to index in {index_time:.2f}s")
            
            # Store documents and metadata
            vector_store.documents.extend(batch)
            vector_store.metadata.extend([doc.get("metadata", {}) for doc in batch])
            
            processed += len(batch)
            batch_time = time.time() - batch_start_time
            
            # Progress summary
            elapsed_time = time.time() - overall_start_time
            avg_time_per_batch = elapsed_time / (batch_idx + 1)
            remaining_batches = total_batches - (batch_idx + 1)
            eta = remaining_batches * avg_time_per_batch

            # Display progress using Rich / 使用Rich显示进度
            display_batch_progress(
                batch_idx, total_batches, len(batch),
                processed, total_docs, batch_time,
                elapsed_time, eta
            )

            # Also log for file records / 同时记录到日志文件
            logger.info(f"  ✅ Batch completed in {batch_time:.2f}s")
            logger.info(f"  📈 Progress: {processed}/{total_docs} documents ({processed/total_docs*100:.1f}%)")
            logger.info(f"  ⏱️ Elapsed: {elapsed_time:.1f}s | ETA: {eta:.1f}s")

            # Memory cleanup
            del embeddings, texts
            gc.collect()
            
            # Small delay to prevent overwhelming the system
            time.sleep(0.1)
            
    except Exception as e:
        logger.error(f"❌ Error processing batch {batch_idx + 1}: {e}")
        raise
    
    # Save the vector store
    total_time = time.time() - overall_start_time
    console.print("💾 Saving vector store...", style="bold yellow")
    logger.info("💾 Saving vector store...")
    save_start = time.time()
    vector_store.save(vector_store_path)
    save_time = time.time() - save_start

    # Create final summary table / 创建最终摘要表格
    summary_table = Table(title="🎉 Vector Store Creation Summary / 向量存储创建摘要", show_header=True, header_style="bold green")
    summary_table.add_column("指标", style="cyan", no_wrap=True)
    summary_table.add_column("值", style="green")

    summary_table.add_row("处理文档数", str(len(vector_store.documents)))
    summary_table.add_row("向量维度", str(vector_store.dimension))
    summary_table.add_row("保存路径", vector_store_path)
    summary_table.add_row("总处理时间", f"{total_time:.2f} 秒")
    summary_table.add_row("保存时间", f"{save_time:.2f} 秒")
    summary_table.add_row("平均每文档时间", f"{total_time/len(documents):.3f} 秒")

    console.print(summary_table)
    console.print(Panel.fit("✅ Vector Store Creation Completed Successfully! / 向量存储创建成功完成！", style="bold green"))

    # Also log for file records / 同时记录到日志文件
    logger.info("=" * 80)
    logger.info("🎉 VECTOR STORE CREATION COMPLETED SUCCESSFULLY!")
    logger.info("=" * 80)
    logger.info(f"📊 Total documents processed: {len(vector_store.documents)}")
    logger.info(f"📏 Vector dimension: {vector_store.dimension}")
    logger.info(f"💾 Saved to: {vector_store_path}")
    logger.info(f"⏱️ Total processing time: {total_time:.2f} seconds")
    logger.info(f"💾 Save time: {save_time:.2f} seconds")
    logger.info(f"📈 Average time per document: {total_time/len(documents):.3f} seconds")
    logger.info("=" * 80)
    
    return vector_store


def test_vector_store(vector_store):
    """
    Test the created vector store
    测试创建的向量存储
    """
    console.print(Panel.fit("🧪 Testing Vector Store / 测试向量存储", style="bold blue"))
    logger.info("🧪 Testing vector store...")

    test_queries = [
        "头痛怎么办",
        "发烧如何处理",
        "咳嗽的原因",
        "胃痛需要注意什么",
        "高血压的治疗方法"
    ]

    # Create test results table / 创建测试结果表格
    test_table = Table(title="🔍 Search Test Results / 搜索测试结果", show_header=True, header_style="bold blue")
    test_table.add_column("查询", style="cyan", no_wrap=True)
    test_table.add_column("结果数", style="yellow")
    test_table.add_column("搜索时间", style="green")
    test_table.add_column("最高分数", style="magenta")

    for query in test_queries:
        try:
            start_time = time.time()
            results = vector_store.search(query, top_k=3)
            search_time = time.time() - start_time

            if results:
                top_score = results[0][1]
                test_table.add_row(
                    query,
                    str(len(results)),
                    f"{search_time:.3f}s",
                    f"{top_score:.4f}"
                )

                # Log detailed info / 记录详细信息
                logger.info(f"🔍 Query: '{query}'")
                logger.info(f"  📋 Found {len(results)} results in {search_time:.3f}s")
                logger.info(f"  🏆 Top result score: {top_score:.4f}")
                top_text = results[0][0].get('text', '')[:100]
                logger.info(f"  📝 Top result preview: {top_text}...")
            else:
                test_table.add_row(query, "0", f"{search_time:.3f}s", "N/A")
                logger.warning(f"  ⚠️ No results found for query: {query}")

        except Exception as e:
            test_table.add_row(query, "ERROR", "N/A", "N/A")
            logger.error(f"❌ Error testing query '{query}': {e}")

    console.print(test_table)
    console.print()


def main():
    """Main function / 主函数"""
    # Display main banner / 显示主横幅
    console.print(Panel.fit("🏥 GRAG Medical AI - Vector Store Creation / GRAG医疗AI - 向量存储创建", style="bold white on blue"))
    logger.info("🏥 GRAG Medical AI - Vector Store Creation")
    logger.info("=" * 80)
    
    try:
        # Get configuration
        config_manager = get_config_manager()
        vector_store_path = config_manager.retrieval.vector_db.get("save_path", "data/embeddings/faiss_index")
        knowledge_base_path = "data/processed/knowledge_base.jsonl"
        
        # Check if knowledge base exists
        if not Path(knowledge_base_path).exists():
            logger.error(f"❌ Knowledge base not found: {knowledge_base_path}")
            logger.info("Please run 'python scripts/01_download_data.py' first to create the knowledge base")
            return
        
        # Load knowledge base
        documents = load_knowledge_base(knowledge_base_path)
        if not documents:
            logger.error("❌ No documents loaded from knowledge base")
            return
        
        # Create vector store
        vector_store = create_vector_store_with_progress(
            documents=documents,
            vector_store_path=vector_store_path,
            config_manager=config_manager
        )
        
        # Test the vector store
        test_vector_store(vector_store)

        # Display next steps / 显示下一步
        next_steps_table = Table(title="🎯 Next Steps / 下一步", show_header=True, header_style="bold cyan")
        next_steps_table.add_column("步骤", style="cyan", no_wrap=True)
        next_steps_table.add_column("命令", style="green")

        next_steps_table.add_row("1. SFT训练", "python scripts/04_train_sft.py")
        next_steps_table.add_row("2. GRPO训练", "python scripts/05_train_grpo.py")
        next_steps_table.add_row("3. 启动前端", "python scripts/06_run_app.py")

        console.print(next_steps_table)

        logger.info("🎯 NEXT STEPS:")
        logger.info("1. Run SFT training: python scripts/04_train_sft.py")
        logger.info("2. Run GRPO training: python scripts/05_train_grpo.py")
        logger.info("3. Start frontend: python scripts/06_run_app.py")
        logger.info("=" * 80)
        
    except Exception as e:
        logger.error(f"❌ Error in vector store creation: {e}")
        raise


if __name__ == "__main__":
    main()
