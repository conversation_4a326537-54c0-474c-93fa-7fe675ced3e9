#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Warnings Script / 修复警告脚本

This script fixes common warnings in the GRAG project, particularly the pkg_resources deprecation warning.
该脚本修复GRAG项目中的常见警告，特别是pkg_resources弃用警告。
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and return success status / 运行命令并返回成功状态"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            if result.stdout.strip():
                print(f"📝 Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} failed")
            if result.stderr.strip():
                print(f"🚨 Error: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"❌ {description} failed with exception: {e}")
        return False

def fix_setuptools_version():
    """Fix setuptools version to avoid pkg_resources warnings / 修复setuptools版本以避免pkg_resources警告"""
    print("🔧 Fixing setuptools version...")
    
    # Check current setuptools version
    try:
        import setuptools
        current_version = setuptools.__version__
        print(f"📦 Current setuptools version: {current_version}")
        
        # Parse version number
        major_version = int(current_version.split('.')[0])
        
        if major_version >= 81:
            print("⚠️ Setuptools version >= 81 detected, downgrading to avoid pkg_resources warnings")
            
            # Downgrade setuptools
            success = run_command(
                "pip install 'setuptools<81'",
                "Downgrading setuptools to version < 81"
            )
            
            if success:
                print("✅ Setuptools downgraded successfully")
            else:
                print("❌ Failed to downgrade setuptools")
                return False
        else:
            print("✅ Setuptools version is compatible")
            
    except ImportError:
        print("❌ Setuptools not found, installing compatible version")
        success = run_command(
            "pip install 'setuptools<81'",
            "Installing compatible setuptools version"
        )
        if not success:
            return False
    
    return True

def update_jieba():
    """Update jieba to latest version / 更新jieba到最新版本"""
    print("📚 Updating jieba...")
    
    # Try to update jieba to latest version
    success = run_command(
        "pip install --upgrade jieba",
        "Updating jieba to latest version"
    )
    
    return success

def install_warning_dependencies():
    """Install dependencies for warning management / 安装警告管理依赖"""
    print("📦 Installing warning management dependencies...")
    
    # Install or upgrade packages that help with warning management
    packages = [
        "setuptools<81",
        "wheel",
        "pip>=23.0"
    ]
    
    for package in packages:
        success = run_command(
            f"pip install '{package}'",
            f"Installing/updating {package}"
        )
        if not success:
            print(f"⚠️ Failed to install {package}, continuing...")
    
    return True

def test_warnings_fix():
    """Test if warnings are fixed / 测试警告是否已修复"""
    print("🧪 Testing warnings fix...")
    
    # Test script content
    test_script = '''
import sys
import os
import warnings

# Capture warnings
warnings.simplefilter("always")

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    # Import utils (which should initialize warning management)
    import utils
    print("✅ Utils imported successfully")
    
    # Test jieba import
    import jieba
    result = list(jieba.cut("测试"))
    print(f"✅ Jieba test successful: {result}")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    sys.exit(1)

print("🎉 All tests passed!")
'''
    
    # Write test script
    test_file = Path("temp_warning_test.py")
    try:
        with open(test_file, "w", encoding="utf-8") as f:
            f.write(test_script)
        
        # Run test
        success = run_command(
            f"python {test_file}",
            "Running warning fix test"
        )
        
        # Clean up
        test_file.unlink()
        
        return success
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        if test_file.exists():
            test_file.unlink()
        return False

def show_final_status():
    """Show final status and recommendations / 显示最终状态和建议"""
    print("\n" + "=" * 60)
    print("📊 Warning Fix Summary / 警告修复摘要")
    print("=" * 60)
    
    # Check setuptools version
    try:
        import setuptools
        version = setuptools.__version__
        major_version = int(version.split('.')[0])
        
        if major_version < 81:
            print(f"✅ Setuptools version: {version} (compatible)")
        else:
            print(f"⚠️ Setuptools version: {version} (may cause warnings)")
    except ImportError:
        print("❌ Setuptools not found")
    
    # Check environment variables
    env_vars = ["TOKENIZERS_PARALLELISM", "HF_HOME", "WANDB_DISABLED"]
    for var in env_vars:
        value = os.environ.get(var, "Not set")
        status = "✅" if value != "Not set" else "📝"
        print(f"{status} {var}: {value}")
    
    print("\n💡 Recommendations / 建议:")
    print("1. Restart your Python session to ensure changes take effect")
    print("2. The warning management system is automatically initialized when importing utils")
    print("3. If you still see warnings, try running: python scripts/test_warnings_filter.py")
    print("4. For persistent issues, consider using virtual environments")
    
    print("=" * 60)

def main():
    """Main function / 主函数"""
    print("🔧 GRAG Warning Fix Tool / GRAG警告修复工具")
    print("=" * 60)
    print("This tool fixes common warnings, especially pkg_resources deprecation warnings.")
    print("该工具修复常见警告，特别是pkg_resources弃用警告。")
    print("=" * 60)
    print()
    
    success_count = 0
    total_steps = 4
    
    # Step 1: Fix setuptools version
    if fix_setuptools_version():
        success_count += 1
    
    # Step 2: Update jieba
    if update_jieba():
        success_count += 1
    
    # Step 3: Install warning dependencies
    if install_warning_dependencies():
        success_count += 1
    
    # Step 4: Test warnings fix
    if test_warnings_fix():
        success_count += 1
    
    # Show final status
    show_final_status()
    
    # Final result
    print(f"\n🎯 Results: {success_count}/{total_steps} steps completed successfully")
    
    if success_count == total_steps:
        print("🎉 All warning fixes applied successfully!")
        print("💡 You may need to restart your Python session for all changes to take effect.")
    else:
        print("⚠️ Some warning fixes failed. Check the output above for details.")
        print("💡 You can still use the warning management system by importing utils.")
    
    return success_count == total_steps

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
