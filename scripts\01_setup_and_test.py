#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GRAG Setup and Test Script / GRAG设置和测试脚本

This script performs system setup verification and tests Qwen3 models functionality.
该脚本执行系统设置验证并测试Qwen3模型功能。
"""

import sys
import os
import torch
import numpy as np
from pathlib import Path

# Add project root to path / 将项目根目录添加到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_system_requirements():
    """Test system requirements / 测试系统要求"""
    print("🔍 Testing System Requirements")
    print("-" * 40)
    
    # Test Python version / 测试Python版本
    python_version = sys.version_info
    print(f"Python Version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    if python_version < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    else:
        print("✅ Python version OK")
    
    # Test PyTorch / 测试PyTorch
    try:
        print(f"PyTorch Version: {torch.__version__}")
        print(f"CUDA Available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA Version: {torch.version.cuda}")
            print(f"GPU Count: {torch.cuda.device_count()}")
            print(f"GPU Name: {torch.cuda.get_device_name(0)}")
        print("✅ PyTorch OK")
    except Exception as e:
        print(f"❌ PyTorch error: {e}")
        return False
    
    # Test Transformers / 测试Transformers
    try:
        import transformers
        print(f"Transformers Version: {transformers.__version__}")
        
        # Check if version supports Qwen3 / 检查版本是否支持Qwen3
        version_parts = transformers.__version__.split('.')
        major, minor = int(version_parts[0]), int(version_parts[1])
        if major > 4 or (major == 4 and minor >= 51):
            print("✅ Transformers version supports Qwen3")
        else:
            print("⚠️  Transformers version may not support Qwen3 (requires >=4.51.0)")
    except Exception as e:
        print(f"❌ Transformers error: {e}")
        return False
    
    # Test FAISS / 测试FAISS
    try:
        import faiss
        print(f"FAISS Version: {faiss.__version__}")
        
        # Test GPU support / 测试GPU支持
        try:
            faiss.StandardGpuResources()
            print("✅ FAISS GPU support available")
        except:
            print("ℹ️  FAISS GPU support not available (CPU only)")
        
        print("✅ FAISS OK")
    except Exception as e:
        print(f"❌ FAISS error: {e}")
        return False
    
    return True

def test_qwen3_models():
    """Test Qwen3 models functionality / 测试Qwen3模型功能"""
    print("\n🧠 Testing Qwen3 Models")
    print("-" * 40)
    
    try:
        from src.utils import get_logger, get_config_manager
        from src.retrieval.enhanced_vector_store import MedicalVectorStore
        from src.retrieval.reranker import MedicalReranker
        
        # Test embedding model / 测试嵌入模型
        print("Testing Qwen3 embedding model...")
        vector_store = MedicalVectorStore()
        vector_store.load_embedding_model()
        
        test_texts = [
            "什么是高血压？",
            "高血压是一种常见的心血管疾病。"
        ]
        embeddings = vector_store.encode_texts(test_texts)
        print(f"✅ Embedding model working - shape: {embeddings.shape}")
        
        # Test reranker model / 测试重排序模型
        print("Testing Qwen3 reranker model...")
        reranker = MedicalReranker()
        reranker.load_reranker_model()
        
        query = "高血压的治疗方法"
        documents = [
            {"text": "高血压的治疗包括药物治疗和生活方式干预。", "score": 0.8},
            {"text": "糖尿病是一种代谢性疾病。", "score": 0.6}
        ]
        reranked = reranker.rerank(query, documents, top_k=2)
        print(f"✅ Reranker model working - returned {len(reranked)} documents")
        
        return True
        
    except Exception as e:
        print(f"❌ Qwen3 models test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """Test configuration files / 测试配置文件"""
    print("\n⚙️  Testing Configuration")
    print("-" * 40)
    
    try:
        # Check config file exists / 检查配置文件是否存在
        config_path = Path("configs/config.yaml")
        if not config_path.exists():
            print("❌ Configuration file not found")
            return False
        
        # Load and validate config / 加载并验证配置
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Check Qwen3 models in config / 检查配置中的Qwen3模型
        embedding_model = config.get('retrieval', {}).get('embedding', {}).get('model_name', '')
        reranker_model = config.get('retrieval', {}).get('reranking', {}).get('model_name', '')
        
        if 'Qwen3-Embedding' in embedding_model:
            print("✅ Qwen3 embedding model configured")
        else:
            print(f"⚠️  Embedding model: {embedding_model}")
        
        if 'Qwen3-Reranker' in reranker_model:
            print("✅ Qwen3 reranker model configured")
        else:
            print(f"⚠️  Reranker model: {reranker_model}")
        
        print("✅ Configuration file OK")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def main():
    """Main function / 主函数"""
    print("🚀 GRAG Setup and Test")
    print("=" * 50)
    
    # Test results / 测试结果
    test_results = {}
    
    # Run tests / 运行测试
    test_results["system"] = test_system_requirements()
    test_results["config"] = test_configuration()
    
    if test_results["system"] and test_results["config"]:
        test_results["qwen3"] = test_qwen3_models()
    else:
        print("\n❌ Prerequisites not met, skipping Qwen3 tests")
        test_results["qwen3"] = False
    
    # Summary / 总结
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("-" * 20)
    
    passed = sum(test_results.values())
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for GRAG.")
        print("\n📝 Next steps:")
        print("   1. Download data: python scripts/02_download_data.py")
        print("   2. Create vector store: python scripts/03_create_vector_store.py")
        print("   3. Run application: python scripts/06_run_app.py")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
        print("💡 Troubleshooting:")
        print("   1. Run: python scripts/00_install_dependencies.py")
        print("   2. Check internet connection for model downloads")
        print("   3. Ensure sufficient GPU memory if using GPU")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
