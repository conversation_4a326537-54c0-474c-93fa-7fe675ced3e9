# 数据配置
data:
  dataset_name: michael<PERSON>zhu/ChatMed_Consult_Dataset  # 数据集名称（HuggingFace数据集）
  max_samples: 1000  # 最大样本数（-1表示使用全部）
  # 数据预处理
  preprocessing:
    clean_text: true  # 是否清理文本
    max_length: 2048  # 最大文本长度
    min_length: 10  # 最小文本长度
    remove_duplicates: true  # 是否移除重复项
  # 数据分割配置
  train_split: 0.8  # 训练集比例
  val_split: 0.1  # 验证集比例
  test_split: 0.1  # 测试集比例
# 环境配置
environment:
  device: auto  # 设备选择（"auto", "cpu", "cuda", "mps"）
  seed: 3407  # 随机种子，用于结果复现
  deterministic: true  # 是否使用确定性算法
  mixed_precision: true  # 是否使用混合精度训练
  compile_model: false  # 是否编译模型（PyTorch 2.0+）
# 评估配置
evaluation:
  # 自动评估指标
  metrics:
  - rouge  # ROUGE分数
  - bleu  # BLEU分数
  - bert_score  # BERTScore
  - medical_accuracy  # 医疗准确性
  - safety_score  # 安全性分数
  - relevance_score  # 相关性分数
  - retrieval_precision  # 检索精确率
  - retrieval_recall  # 检索召回率

  # 医疗专项评估
  medical_eval:
    enabled: true  # 是否启用医疗评估
    criteria:  # 评估标准
    - symptom_understanding  # 症状理解
    - diagnosis_accuracy  # 诊断准确性
    - treatment_appropriateness  # 治疗适当性
    - risk_assessment  # 风险评估

  # 人工评估
  human_eval:
    enabled: false  # 是否启用人工评估
    sample_size: 100  # 评估样本数
    evaluators:  # 评估者类型
    - medical_expert  # 医疗专家
    - general_user  # 普通用户
# 前端配置
frontend:
  title: GRAG - 医疗问诊智能助手  # 应用标题
  subtitle: 基于GRPO优化的医疗RAG系统  # 应用副标题
  description: 基于GRPO优化的医疗RAG智能问诊助手  # 应用描述
  language: zh-CN  # 界面语言
  theme: light  # 主题（"light", "dark"）
  max_history: 10  # 最大对话历史
  # 显示选项
  show_confidence: true  # 是否显示置信度
  show_sources: true  # 是否显示来源
  show_retrieval_details: true  # 是否显示检索详情
# LangGraph工作流配置
langgraph:
  # 工作流节点
  nodes:
  - query_analysis  # 查询分析
  - retrieval  # 检索
  - reranking  # 重排序
  - generation  # 生成
  - self_correction  # 自我修正
  - web_search  # 网络搜索
  - final_answer  # 最终答案

  # 工作流边
  edges:
    # 条件边
    conditional_edges:
    # 从查询分析
    - from: query_analysis
      conditions:
      - condition: needs_web_search  # 需要网络搜索
        to: web_search
      - condition: standard_retrieval  # 标准检索
        to: retrieval
    # 从生成
    - from: generation
      conditions:
      - condition: needs_correction  # 需要修正
        to: self_correction
      - condition: satisfactory  # 满意的结果
        to: final_answer
# 日志配置
logging:
  level: INFO  # 日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
  file: ./logs/grag.log  # 日志文件路径
  format: '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}'  # 日志格式
  rotation: 1 day  # 日志轮转周期
  retention: 30 days  # 日志保留时间
# 模型配置
model:
  base_model: Qwen/Qwen3-8B  # 基础模型名称
  model_type: qwen3  # 模型类型
  max_length: 8192  # 最大序列长度
  load_in_4bit: true  # 是否使用4位量化
  # 生成参数
  temperature: 0.7  # 温度参数（0.0-2.0）
  top_k: 50  # Top-K采样
  top_p: 0.9  # Top-P（核）采样
  # LoRA配置
  lora_config:
    r: 32  # LoRA秩
    lora_alpha: 64  # LoRA alpha参数
    lora_dropout: 0.0  # LoRA dropout率
    bias: none  # 偏置设置（"none", "all", "lora_only"）
    target_modules:  # LoRA目标模块
    - q_proj  # 查询投影
    - k_proj  # 键投影
    - v_proj  # 值投影
    - o_proj  # 输出投影
    - gate_proj  # 门投影
    - up_proj  # 上投影
    - down_proj  # 下投影
# 检索配置
retrieval:
  # 向量数据库配置
  vector_db:
    type: faiss  # 向量数据库类型（faiss）
    dimension: 1024  # 向量维度（Qwen3-Embedding-0.6B: 1024）
    index_type: IndexFlatIP  # FAISS索引类型（IndexFlatIP, IndexFlatL2, IndexIVFFlat, IndexHNSWFlat）
    save_path: ./data/embeddings/faiss_index  # 向量存储保存路径
    creation_batch_size: 10  # 向量存储创建时的批次大小（建议: 4-8, 内存不足时可调小）
    # GPU加速配置
    use_gpu: auto  # GPU使用模式（"auto": 自动检测, "cpu": 强制CPU, "gpu": 强制GPU）
    gpu_device: 0  # GPU设备ID（当use_gpu为"gpu"时使用）

  # 嵌入模型配置
  embedding:
    model_name: Qwen/Qwen3-Embedding-0.6B  # 嵌入模型名称（Qwen3嵌入模型, 支持100+语言）
    batch_size: 32  # 批处理大小（GPU内存不足时可调小）
    max_length: 8192  # 最大文本长度（Qwen3支持32K上下文，这里设置8K平衡性能）
    device: auto  # 设备选择（"auto", "cpu", "cuda"）
    normalize_embeddings: true  # 是否归一化嵌入
    # Qwen3特有配置
    use_instruction: true  # 是否使用指令提示（可提升1-5%性能）
    instruction_template: 为这个句子生成表示以用于检索相关文章：  # 指令模板（中文）
    # instruction_template: Given a web search query, retrieve relevant passages that answer the query  # 英文指令模板

  # 搜索配置
  search:
    top_k: 10  # 初始检索数量
    similarity_threshold: 0.7  # 相似度阈值（0.0-1.0）
    adaptive_threshold: true  # 自适应阈值调整

  # 重排序配置
  reranking:
    enabled: true  # 是否启用重排序
    model_name: Qwen/Qwen3-Reranker-0.6B  # 重排序模型名称（Qwen3重排序模型, 支持100+语言）
    top_k_rerank: 5  # 重排序后保留数量
    batch_size: 16  # 重排序批处理大小（GPU内存不足时可调小）
    # Qwen3特有配置
    use_instruction: true  # 是否使用指令提示
    instruction_template: 给定网络搜索查询，检索回答查询的相关段落  # 指令模板

  # 自我修正配置
  self_correction:
    enabled: true  # 是否启用自我修正
    confidence_threshold: 0.8  # 置信度阈值，用于触发修正
    max_iterations: 3  # 最大修正迭代次数
    strategies:  # 修正策略
    - query_expansion  # 查询扩展
    - parameter_adjustment  # 参数调整
    - web_search_fallback  # 网络搜索回退
# 奖励模型配置
reward_models:
  # 医疗准确性奖励
  medical_accuracy:
    type: llm_based  # 奖励类型
    model_name: gpt-3.5-turbo  # 评估模型
    prompt_template: medical_accuracy_grader  # 提示模板
    weight: 0.35  # 在最终奖励中的权重

  # 相关性奖励
  relevance:
    type: embedding_similarity  # 基于嵌入相似性
    model_name: Qwen/Qwen3-Embedding-0.6B  # 嵌入模型
    threshold: 0.7  # 相似性阈值
    weight: 0.25  # 在最终奖励中的权重

  # 安全性奖励
  safety:
    type: rule_based  # 基于规则
    weight: 0.25  # 在最终奖励中的权重
    rules:  # 安全规则
    - no_harmful_advice  # 无有害建议
    - medical_disclaimer  # 医疗免责声明
    - professional_referral  # 专业转诊

  # 完整性奖励
  completeness:
    type: length_and_coverage  # 基于长度和覆盖度
    min_length: 50  # 最小长度
    weight: 0.1  # 在最终奖励中的权重
    coverage_keywords:  # 覆盖关键词
    - 症状  # 症状
    - 建议  # 建议
    - 注意事项  # 注意事项

  # 格式合规性奖励
  format_compliance:
    type: regex_based  # 基于正则表达式
    weight: 0.05  # 在最终奖励中的权重
    required_sections:  # 必需章节
    - 分析  # 分析
    - 建议  # 建议
# 训练配置
training:
  # 监督微调（SFT）
  sft:
    output_dir: ./models/sft  # 输出目录
    num_train_epochs: 3  # 训练轮数
    learning_rate: 2e-4  # 学习率
    per_device_train_batch_size: 2  # 每设备训练批次大小
    per_device_eval_batch_size: 2  # 每设备评估批次大小
    gradient_accumulation_steps: 8  # 梯度累积步数
    max_length: 2048  # 最大序列长度
    # 优化设置
    optim: adamw_8bit  # 优化器
    lr_scheduler_type: linear  # 学习率调度器
    warmup_ratio: 0.05  # 预热比例
    weight_decay: 0.01  # 权重衰减
    # 日志和保存
    logging_steps: 10  # 日志记录步数
    save_steps: 500  # 保存步数
    eval_steps: 500  # 评估步数

  # 群体相对策略优化（GRPO）
  grpo:
    output_dir: ./models/grpo  # 输出目录
    num_train_epochs: 1  # 训练轮数
    learning_rate: 5e-6  # 学习率（通常比SFT更小）
    per_device_train_batch_size: 1  # 每设备训练批次大小
    # 生成设置
    max_prompt_length: 1024  # 最大提示长度
    max_completion_length: 4096  # 最大完成长度
    num_generations: 4  # 每个提示的生成数量
    temperature: 1.0  # 生成温度
    top_p: 0.85  # Top-p采样
    # 奖励函数
    reward_functions:  # 使用的奖励函数
    - medical_accuracy  # 医疗准确性
    - relevance  # 相关性
    - safety  # 安全性
    - completeness  # 完整性
    - format_compliance  # 格式合规性
# 网络搜索配置
web_search:
  enabled: true  # 是否启用网络搜索
  provider: tavily  # 搜索提供商（"tavily", "google", "bing"）
  api_key: ${TAVILY_API_KEY}  # API密钥（从环境变量获取）
  max_results: 5  # 最大搜索结果数
  search_depth: advanced  # 搜索深度（"basic", "advanced"）
  time_range: month  # 时间范围（"day", "week", "month", "year"）
  fallback_enabled: true  # 是否启用回退搜索
  # 可信域名白名单
  domains_whitelist:
  - baike.baidu.com  # 百度百科
  - zh.wikipedia.org  # 中文维基百科
  - med.sina.com.cn  # 新浪医疗
