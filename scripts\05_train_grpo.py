#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GRPO Training Script / GRPO训练脚本

This script trains the medical model using Group Relative Policy Optimization (GRPO).
该脚本使用群体相对策略优化(GRPO)训练医疗模型。
"""

import os
import sys
import argparse
import wandb
from pathlib import Path

# Add src to path / 添加src到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from utils import get_logger, config_manager, set_seed
from training import MedicalGRPOTrainer
from data_processing import MedicalDatasetLoader

logger = get_logger(__name__)


def train_grpo_model(
    data_dir: str = "data/processed",
    sft_model_path: str = None,
    output_dir: str = None,
    use_wandb: bool = False,
    max_steps: int = None
):
    """
    Train GRPO model / 训练GRPO模型
    
    Args:
        data_dir: Directory containing processed data / 包含处理数据的目录
        sft_model_path: Path to SFT model / SFT模型路径
        output_dir: Output directory for model / 模型输出目录
        use_wandb: Whether to use Weights & Biases / 是否使用Weights & Biases
        max_steps: Maximum training steps / 最大训练步数
    """
    logger.info("Starting GRPO training...")
    
    # Set random seed / 设置随机种子
    set_seed(config_manager.environment.seed)
    
    # Initialize Weights & Biases if requested / 如果请求则初始化Weights & Biases
    if use_wandb:
        wandb.init(
            project="medical-rag-grpo",
            name=f"grpo-{config_manager.model.base_model.split('/')[-1]}",
            config={
                "model": config_manager.model.__dict__,
                "training": config_manager.training.grpo,
                "reward_models": config_manager.reward_models
            }
        )
        logger.info("Weights & Biases initialized")
    
    # Update configuration / 更新配置
    if output_dir:
        config_manager.training.grpo["output_dir"] = output_dir
    
    if max_steps:
        config_manager.training.grpo["max_steps"] = max_steps
    
    # Determine SFT model path / 确定SFT模型路径
    if not sft_model_path:
        sft_model_path = config_manager.training.sft["output_dir"]
        if not Path(sft_model_path).exists():
            logger.warning(f"SFT model not found at {sft_model_path}")
            logger.info("Will use base model instead")
            sft_model_path = None
    
    # Initialize GRPO trainer / 初始化GRPO训练器
    logger.info("Initializing GRPO trainer...")
    trainer = MedicalGRPOTrainer()
    
    # Load model / 加载模型
    logger.info(f"Loading model from: {sft_model_path or 'base model'}")
    trainer.load_model(sft_model_path)
    
    # Setup reward functions / 设置奖励函数
    logger.info("Setting up reward functions...")
    trainer.setup_reward_functions()
    
    # Prepare datasets / 准备数据集
    logger.info("Preparing GRPO training datasets...")
    
    # Check if processed data exists / 检查处理数据是否存在
    train_file = Path(data_dir) / "train.jsonl"
    if not train_file.exists():
        logger.error(f"Training data not found at {train_file}")
        logger.info("Please run 'python scripts/download_data.py' first")
        sys.exit(1)
    
    # Load training data / 加载训练数据
    loader = MedicalDatasetLoader()
    train_data = loader.load_processed_data("train", data_dir)
    val_data = loader.load_processed_data("val", data_dir)
    
    # Prepare GRPO format / 准备GRPO格式
    grpo_train_data = loader.prepare_grpo_data(train_data)
    grpo_val_data = loader.prepare_grpo_data(val_data)
    
    # Convert to datasets / 转换为数据集
    train_dataset = trainer.prepare_dataset_from_data(grpo_train_data)
    eval_dataset = trainer.prepare_dataset_from_data(grpo_val_data) if grpo_val_data else None
    
    logger.info(f"Training samples: {len(train_dataset)}")
    if eval_dataset:
        logger.info(f"Validation samples: {len(eval_dataset)}")
    
    # Start training / 开始训练
    logger.info("Starting GRPO training...")
    trainer.train(train_dataset, eval_dataset)
    
    # Save model / 保存模型
    logger.info("Saving trained GRPO model...")
    trainer.save_model()
    
    # Test generation / 测试生成
    logger.info("Testing GRPO model generation...")
    test_queries = [
        "我最近总是头痛，应该怎么办？",
        "发烧了需要注意什么？",
        "咳嗽一直不好，可能是什么原因？",
        "胃痛应该如何缓解？",
        "失眠有什么好的治疗方法？"
    ]
    
    for query in test_queries:
        try:
            response = trainer.generate_response(query, max_tokens=512)
            
            logger.info(f"Query: {query}")
            logger.info(f"Response: {response}")
            logger.info("-" * 50)
            
        except Exception as e:
            logger.warning(f"Generation test failed for query '{query}': {e}")
    
    # Log completion / 记录完成
    logger.info("GRPO training completed successfully!")
    
    if use_wandb:
        wandb.finish()
    
    # Print summary / 打印摘要
    print("\n" + "="*60)
    print("GRPO TRAINING COMPLETED / GRPO训练完成")
    print("="*60)
    print(f"Base model: {config_manager.model.base_model}")
    print(f"SFT model: {sft_model_path or 'None'}")
    print(f"Output directory: {config_manager.training.grpo['output_dir']}")
    print(f"Training samples: {len(train_dataset)}")
    if eval_dataset:
        print(f"Validation samples: {len(eval_dataset)}")
    print(f"Reward functions: {len(trainer.reward_functions)}")
    print("="*60)
    print("NEXT STEPS / 下一步:")
    print("1. Test the model: python scripts/test_model.py")
    print("2. Run evaluation: python scripts/evaluate_model.py")
    print("3. Start frontend: streamlit run src/frontend/app.py")
    print("="*60)


def main():
    """Main function / 主函数"""
    parser = argparse.ArgumentParser(description="Train GRPO model for medical RAG")
    
    parser.add_argument(
        "--data-dir",
        type=str,
        default="data/processed",
        help="Directory containing processed training data"
    )
    
    parser.add_argument(
        "--sft-model",
        type=str,
        default=None,
        help="Path to SFT model (default: from config)"
    )
    
    parser.add_argument(
        "--output-dir",
        type=str,
        default=None,
        help="Output directory for trained model (default: from config)"
    )
    
    parser.add_argument(
        "--wandb",
        action="store_true",
        help="Use Weights & Biases for logging"
    )
    
    parser.add_argument(
        "--max-steps",
        type=int,
        default=None,
        help="Maximum training steps (default: from config)"
    )
    
    args = parser.parse_args()
    
    try:
        train_grpo_model(
            data_dir=args.data_dir,
            sft_model_path=args.sft_model,
            output_dir=args.output_dir,
            use_wandb=args.wandb,
            max_steps=args.max_steps
        )
    except Exception as e:
        logger.error(f"Error in GRPO training: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
