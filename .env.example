# GRAG System Environment Variables
# Copy this file to .env and fill in your actual values

# API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here
HUGGINGFACE_TOKEN=your_huggingface_token_here

# Model Configuration
BASE_MODEL_NAME=Qwen/Qwen3-8B
EMBEDDING_MODEL_NAME=Qwen/Qwen3-Embedding-0.6B
RERANKER_MODEL_NAME=Qwen/Qwen3-Reranker-0.6B

# Data Configuration
DATASET_NAME=michaelwzhu/ChatMed_Consult_Dataset
MAX_SAMPLES=50000

# Training Configuration
DEVICE=auto  # auto, cpu, cuda
MIXED_PRECISION=true
COMPILE_MODEL=false

# Logging
LOG_LEVEL=INFO
WANDB_PROJECT=grag-medical-rag
WANDB_ENTITY=your_wandb_entity

# Vector Database
VECTOR_DB_PATH=./data/embeddings/faiss_index
VECTOR_DB_DIMENSION=768

# Web Search
WEB_SEARCH_ENABLED=true
MAX_WEB_RESULTS=5

# Frontend
STREAMLIT_PORT=8501
STREAMLIT_HOST=localhost
