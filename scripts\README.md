﻿# GRAG Scripts Directory / GRAG脚本目录

Streamlined scripts for the GRAG project workflow with integrated Qwen3 support.
GRAG项目工作流的精简脚本，集成Qwen3支持。

## 🚀 Quick Start / 快速开始

Execute scripts in numerical order for complete setup:
按数字顺序执行脚本以完成完整设置：

```bash
# Step 0: Install dependencies and setup
python scripts/00_install_dependencies.py --quick

# Step 1: Verify setup and test models
python scripts/01_setup_and_test.py

# Step 2: Download and process data
python scripts/02_download_data.py

# Step 3: Create vector store with Qwen3 embeddings
python scripts/03_create_vector_store.py

# Step 4: Train SFT model (optional)
python scripts/04_train_sft.py

# Step 5: Train GRPO model (optional)
python scripts/05_train_grpo.py

# Step 6: Run the application
python scripts/06_run_app.py
```

## 📋 Script Details / 脚本详情

| Script | Purpose | Duration | Output |
|--------|---------|----------|---------|
| `00_install_dependencies.py` | 🔧 Install deps & setup Qwen3 | ~5-15min | Complete environment |
| `01_setup_and_test.py` | ✅ Verify setup & test models | ~2-5min | System validation |
| `02_download_data.py` | 📥 Download & process data | ~30s | Training data, knowledge base |
| `03_create_vector_store.py` | 🗂️ Create FAISS index with Qwen3 | ~5-10min | Vector embeddings |
| `04_train_sft.py` | 🎓 Supervised fine-tuning | Variable | SFT model |
| `05_train_grpo.py` | 🎯 Policy optimization | Variable | GRPO model |
| `06_run_app.py` | 🌐 Launch web interface | Continuous | Streamlit app |

## Key Features / 主要特性

### 📊 Progress Tracking
- Real-time progress bars
- ETA calculations  
- Batch processing updates
- Memory usage optimization

### 🔄 Error Recovery
- Graceful error handling
- Detailed logging
- Continuation from failures
- Resource cleanup

### 🎯 User-Friendly
- Clear step-by-step execution
- Intuitive naming convention
- Comprehensive documentation
- Visual progress indicators

## 📋 Prerequisites / 前提条件

1. **Environment / 环境**: Python 3.8+ with virtual environment activated
2. **Dependencies / 依赖**: `python install_dependencies.py --quick`
3. **Hardware / 硬件**: 8GB+ RAM recommended, GPU optional for acceleration
4. **Models / 模型**: Qwen3 models will be downloaded automatically

## Troubleshooting / 故障排除

### If a script fails / 如果脚本失败:

1. **Check the logs** for detailed error messages
2. **Verify prerequisites** are met
3. **Restart from the failed step** (scripts are designed to be re-runnable)
4. **Check available resources** (memory, disk space)

### Common solutions / 常见解决方案:

```bash
# Restart virtual environment
deactivate
source .venv/bin/activate  # Linux/Mac
.venv\Scripts\activate     # Windows

# Clear cache if needed
rm -rf scripts/__pycache__

# Check system resources
# Ensure sufficient memory and disk space
```

## ✅ Success Indicators / 成功指标

| Step | Success Indicator | Expected Output |
|------|------------------|-----------------|
| **00** | Dependencies installed, Qwen3 configured | "🎉 Installation completed!" |
| **01** | System verified, models tested | "🎉 All tests passed!" |
| **02** | Data downloaded and processed | "Knowledge base created with X items" |
| **03** | Vector store created | "Vector store with X documents indexed" |
| **04** | SFT model trained (optional) | "SFT model saved successfully" |
| **05** | GRPO model trained (optional) | "GRPO model optimized" |
| **06** | Application running | "App accessible at http://localhost:8501" |

## 🎯 Next Steps / 下一步

After successful execution:

1. **Access the web interface** at http://localhost:8501
2. **Test medical queries** to verify Qwen3 functionality
3. **Customize configurations** in `configs/config.yaml`
4. **Review logs** for any warnings or optimization opportunities

## 🔧 Key Features / 主要特性

- **Integrated Setup**: All dependencies and Qwen3 configuration in one script
- **Comprehensive Testing**: System validation and model testing combined
- **Streamlined Workflow**: Logical sequence from setup to deployment
- **Error Handling**: Robust error detection and troubleshooting guidance

## 📚 Documentation / 文档

- **Installation Guide**: `INSTALL.md`
- **Qwen3 Upgrade Guide**: `QWEN3_UPGRADE_GUIDE.md`
- **Configuration Guide**: `docs/configuration_guide.md`
