# Training Progress and Debug Information Display / 训练进度和调试信息显示

## 概述 / Overview

本文档描述了GRAG项目中训练脚本的进度和调试信息显示功能。我们为SFT和GRPO训练脚本添加了丰富的可视化进度显示和详细的调试信息。

This document describes the progress and debug information display features in GRAG training scripts. We have added rich visual progress displays and detailed debug information for both SFT and GRPO training scripts.

## 功能特性 / Features

### ✅ 已实现的功能 / Implemented Features

#### 1. 彩色日志系统 / Colorized Logging System
- 使用 `loguru` 库提供彩色控制台输出
- 详细的日志格式，包含时间戳、级别、函数名、行号
- 同时支持控制台和文件日志记录
- 可配置的日志级别和轮转策略

#### 2. Rich 可视化界面 / Rich Visual Interface
- 使用 `rich` 库提供美观的表格和面板显示
- 训练配置信息的结构化展示
- 彩色进度条和状态指示器
- 专业的终端界面布局

#### 3. 训练配置显示 / Training Configuration Display

**SFT训练配置表格：**
```
🚀 SFT Training Configuration / SFT训练配置
┏━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓
┃ 配置项       ┃ 值            ┃
┡━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩
│ 基础模型     │ Qwen/Qwen3-8B │
│ 训练轮数     │ 3             │
│ 学习率       │ 2e-4          │
│ 批次大小     │ 2             │
│ 梯度累积步数 │ 8             │
│ 训练样本数   │ 1000          │
│ 验证样本数   │ 200           │
│ 输出目录     │ ./models/sft  │
└──────────────┴───────────────┘
```

**GRPO训练配置表格：**
```
🎯 GRPO Training Configuration / GRPO训练配置
┏━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓
┃ 配置项       ┃ 值            ┃
┡━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩
│ 基础模型     │ Qwen/Qwen3-8B │
│ SFT模型路径  │ ./models/sft  │
│ 训练轮数     │ 1             │
│ 学习率       │ 5e-6          │
│ 批次大小     │ 1             │
│ 生成数量     │ 4             │
│ 奖励函数数量 │ 5             │
│ 训练样本数   │ 800           │
│ 验证样本数   │ 200           │
│ 输出目录     │ ./models/grpo │
└──────────────┴───────────────┘
```

#### 4. 奖励函数信息显示 / Reward Functions Display
```
🏆 Reward Functions / 奖励函数
┏━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━┓
┃ 奖励函数          ┃ 类型            ┃ 权重 ┃
┡━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━┩
│ medical_accuracy  │ LLM-based       │ 0.35 │
│ relevance         │ Embedding-based │ 0.25 │
│ safety            │ Rule-based      │ 0.25 │
│ completeness      │ Length-based    │ 0.10 │
│ format_compliance │ Regex-based     │ 0.05 │
└───────────────────┴─────────────────┴──────┘
```

#### 5. 实时进度显示 / Real-time Progress Display
- 训练步数进度：`📊 Step 100/1000 | Loss: 0.5432 | LR: 2.00e-04`
- 动态进度条显示训练进度
- 损失值和学习率的实时更新
- 训练阶段状态指示

#### 6. Weights & Biases 集成 / W&B Integration
- 支持 `--wandb` 参数启用实验跟踪
- 自动记录训练配置和超参数
- 实时上传训练指标和损失曲线
- 模型性能对比和分析

#### 7. 启动横幅 / Startup Banners
```
╭───────────────────────────────────────────────────╮
│ 🏥 GRAG Medical SFT Training / GRAG医疗SFT训练    │
╰───────────────────────────────────────────────────╯

╭─────────────────────────────────────────────────────╮
│ 🎯 GRAG Medical GRPO Training / GRAG医疗GRPO训练   │
╰─────────────────────────────────────────────────────╯
```

## 使用方法 / Usage

### SFT训练 / SFT Training
```bash
# 基础训练
python scripts/04_train_sft.py

# 使用Weights & Biases
python scripts/04_train_sft.py --wandb

# 自定义配置
python scripts/04_train_sft.py --data-dir data/processed --output-dir models/my_sft --wandb
```

### GRPO训练 / GRPO Training
```bash
# 基础训练
python scripts/05_train_grpo.py

# 指定SFT模型
python scripts/05_train_grpo.py --sft-model models/sft

# 使用Weights & Biases
python scripts/05_train_grpo.py --wandb --max-steps 1000
```

## 日志配置 / Logging Configuration

日志系统通过 `configs/config.yaml` 进行配置：

```yaml
# 日志配置
logging:
  level: INFO  # 日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
  file: ./logs/grag.log  # 日志文件路径
  format: '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}'
  rotation: 1 day  # 日志轮转周期
  retention: 30 days  # 日志保留时间
```

## 依赖包 / Dependencies

确保安装了以下包：
```bash
pip install rich>=13.0.0
pip install tqdm>=4.66.0
pip install loguru>=0.7.2
pip install wandb>=0.16.0  # 可选，用于实验跟踪
```

## 测试 / Testing

运行显示功能测试：
```bash
python scripts/test_training_display.py
```

## 故障排除 / Troubleshooting

### 常见问题 / Common Issues

1. **Rich库显示异常**
   - 确保终端支持Unicode和颜色显示
   - 在Windows上建议使用Windows Terminal

2. **日志文件权限问题**
   - 确保 `logs/` 目录有写入权限
   - 检查磁盘空间是否充足

3. **Weights & Biases连接问题**
   - 确保网络连接正常
   - 检查API密钥配置：`wandb login`

## 性能影响 / Performance Impact

- Rich显示功能对训练性能影响极小（<1%）
- 日志记录开销可忽略不计
- W&B上传可能会轻微影响训练速度，可通过配置调整上传频率

## 向量存储创建显示 / Vector Store Creation Display

### 新增功能 / New Features

我们也为 `03_create_vector_store.py` 添加了相同的Rich显示功能：

#### 1. 配置信息表格 / Configuration Table
```
🗂️ Vector Store Configuration / 向量存储配置
┏━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 配置项   ┃ 值                          ┃
┡━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩
│ 嵌入模型 │ Qwen/Qwen3-Embedding-0.6B   │
│ 向量维度 │ 1024                        │
│ 索引类型 │ IndexFlatIP                 │
│ 批次大小 │ 4                           │
│ 文档总数 │ 5000                        │
│ 保存路径 │ ./data/embeddings/faiss_index │
│ GPU加速  │ auto                        │
└──────────┴─────────────────────────────┘
```

#### 2. 文档加载进度条 / Document Loading Progress
- 使用Rich Progress组件显示文档加载进度
- 实时显示加载速度和剩余时间
- 彩色进度条和旋转指示器

#### 3. 批次处理进度 / Batch Processing Progress
```
🔄 Batch 1/1250 | 4/5000 docs (0.1%)
⏱️  Batch: 2.50s | Elapsed: 2.5s | ETA: 3122.5s
📊 [█░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░] 0.1%
```

#### 4. 创建摘要表格 / Creation Summary Table
```
🎉 Vector Store Creation Summary / 向量存储创建摘要
┏━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ 指标           ┃ 值                          ┃
┡━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩
│ 处理文档数     │ 5000                        │
│ 向量维度       │ 1024                        │
│ 保存路径       │ ./data/embeddings/faiss_index │
│ 总处理时间     │ 125.6 秒                    │
│ 保存时间       │ 3.2 秒                      │
│ 平均每文档时间 │ 0.025 秒                    │
└────────────────┴─────────────────────────────┘
```

#### 5. 搜索测试结果 / Search Test Results
```
🔍 Search Test Results / 搜索测试结果
┏━━━━━━━━━━━━━━━━━━┳━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━┓
┃ 查询             ┃ 结果数 ┃ 搜索时间 ┃ 最高分数 ┃
┡━━━━━━━━━━━━━━━━━━╇━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━┩
│ 头痛怎么办       │ 3      │ 0.045s   │ 0.8542   │
│ 发烧如何处理     │ 3      │ 0.038s   │ 0.7891   │
│ 咳嗽的原因       │ 3      │ 0.042s   │ 0.8123   │
│ 胃痛需要注意什么 │ 3      │ 0.051s   │ 0.7654   │
│ 高血压的治疗方法 │ 3      │ 0.047s   │ 0.8876   │
└──────────────────┴────────┴──────────┴──────────┘
```

### 使用方法 / Usage

```bash
# 创建向量存储（带Rich显示）
python scripts/03_create_vector_store.py

# 测试向量存储显示功能
python scripts/test_vector_store_display.py
```

## 代码解释 / Code Explanation

### ETA计算说明 / ETA Calculation Explanation

您询问的这行代码：
```python
logger.info(f"  ⏱️ Elapsed: {elapsed_time:.1f}s | ETA: {eta:.1f}s")
```

**含义解释：**
- **`elapsed_time`**: 已经过去的时间（从开始处理到现在的总时间）
- **`eta`**: Estimated Time of Arrival，预计完成时间（还需要多长时间完成）
- **`.1f`**: 格式化为保留1位小数的浮点数
- **`⏱️`**: 时钟emoji，表示时间相关信息

**计算方法：**
```python
elapsed_time = time.time() - overall_start_time
avg_time_per_batch = elapsed_time / (batch_idx + 1)
remaining_batches = total_batches - (batch_idx + 1)
eta = remaining_batches * avg_time_per_batch
```

**示例输出：**
`⏱️ Elapsed: 45.2s | ETA: 23.8s` 表示已经运行了45.2秒，预计还需要23.8秒完成。

## 未来改进 / Future Improvements

- [ ] 添加GPU使用率和内存监控
- [ ] 实现训练时间预估功能
- [ ] 添加模型收敛性可视化
- [ ] 支持分布式训练进度同步
- [ ] 集成TensorBoard支持
- [ ] 添加向量存储质量评估指标
- [ ] 实现实时性能监控仪表板
