# Training Progress and Debug Information Display / 训练进度和调试信息显示

## 概述 / Overview

本文档描述了GRAG项目中训练脚本的进度和调试信息显示功能。我们为SFT和GRPO训练脚本添加了丰富的可视化进度显示和详细的调试信息。

This document describes the progress and debug information display features in GRAG training scripts. We have added rich visual progress displays and detailed debug information for both SFT and GRPO training scripts.

## 功能特性 / Features

### ✅ 已实现的功能 / Implemented Features

#### 1. 彩色日志系统 / Colorized Logging System
- 使用 `loguru` 库提供彩色控制台输出
- 详细的日志格式，包含时间戳、级别、函数名、行号
- 同时支持控制台和文件日志记录
- 可配置的日志级别和轮转策略

#### 2. Rich 可视化界面 / Rich Visual Interface
- 使用 `rich` 库提供美观的表格和面板显示
- 训练配置信息的结构化展示
- 彩色进度条和状态指示器
- 专业的终端界面布局

#### 3. 训练配置显示 / Training Configuration Display

**SFT训练配置表格：**
```
🚀 SFT Training Configuration / SFT训练配置
┏━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓
┃ 配置项       ┃ 值            ┃
┡━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩
│ 基础模型     │ Qwen/Qwen3-8B │
│ 训练轮数     │ 3             │
│ 学习率       │ 2e-4          │
│ 批次大小     │ 2             │
│ 梯度累积步数 │ 8             │
│ 训练样本数   │ 1000          │
│ 验证样本数   │ 200           │
│ 输出目录     │ ./models/sft  │
└──────────────┴───────────────┘
```

**GRPO训练配置表格：**
```
🎯 GRPO Training Configuration / GRPO训练配置
┏━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓
┃ 配置项       ┃ 值            ┃
┡━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩
│ 基础模型     │ Qwen/Qwen3-8B │
│ SFT模型路径  │ ./models/sft  │
│ 训练轮数     │ 1             │
│ 学习率       │ 5e-6          │
│ 批次大小     │ 1             │
│ 生成数量     │ 4             │
│ 奖励函数数量 │ 5             │
│ 训练样本数   │ 800           │
│ 验证样本数   │ 200           │
│ 输出目录     │ ./models/grpo │
└──────────────┴───────────────┘
```

#### 4. 奖励函数信息显示 / Reward Functions Display
```
🏆 Reward Functions / 奖励函数
┏━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━┓
┃ 奖励函数          ┃ 类型            ┃ 权重 ┃
┡━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━┩
│ medical_accuracy  │ LLM-based       │ 0.35 │
│ relevance         │ Embedding-based │ 0.25 │
│ safety            │ Rule-based      │ 0.25 │
│ completeness      │ Length-based    │ 0.10 │
│ format_compliance │ Regex-based     │ 0.05 │
└───────────────────┴─────────────────┴──────┘
```

#### 5. 实时进度显示 / Real-time Progress Display
- 训练步数进度：`📊 Step 100/1000 | Loss: 0.5432 | LR: 2.00e-04`
- 动态进度条显示训练进度
- 损失值和学习率的实时更新
- 训练阶段状态指示

#### 6. Weights & Biases 集成 / W&B Integration
- 支持 `--wandb` 参数启用实验跟踪
- 自动记录训练配置和超参数
- 实时上传训练指标和损失曲线
- 模型性能对比和分析

#### 7. 启动横幅 / Startup Banners
```
╭───────────────────────────────────────────────────╮
│ 🏥 GRAG Medical SFT Training / GRAG医疗SFT训练    │
╰───────────────────────────────────────────────────╯

╭─────────────────────────────────────────────────────╮
│ 🎯 GRAG Medical GRPO Training / GRAG医疗GRPO训练   │
╰─────────────────────────────────────────────────────╯
```

## 使用方法 / Usage

### SFT训练 / SFT Training
```bash
# 基础训练
python scripts/04_train_sft.py

# 使用Weights & Biases
python scripts/04_train_sft.py --wandb

# 自定义配置
python scripts/04_train_sft.py --data-dir data/processed --output-dir models/my_sft --wandb
```

### GRPO训练 / GRPO Training
```bash
# 基础训练
python scripts/05_train_grpo.py

# 指定SFT模型
python scripts/05_train_grpo.py --sft-model models/sft

# 使用Weights & Biases
python scripts/05_train_grpo.py --wandb --max-steps 1000
```

## 日志配置 / Logging Configuration

日志系统通过 `configs/config.yaml` 进行配置：

```yaml
# 日志配置
logging:
  level: INFO  # 日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
  file: ./logs/grag.log  # 日志文件路径
  format: '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}'
  rotation: 1 day  # 日志轮转周期
  retention: 30 days  # 日志保留时间
```

## 依赖包 / Dependencies

确保安装了以下包：
```bash
pip install rich>=13.0.0
pip install tqdm>=4.66.0
pip install loguru>=0.7.2
pip install wandb>=0.16.0  # 可选，用于实验跟踪
```

## 测试 / Testing

运行显示功能测试：
```bash
python scripts/test_training_display.py
```

## 故障排除 / Troubleshooting

### 常见问题 / Common Issues

1. **Rich库显示异常**
   - 确保终端支持Unicode和颜色显示
   - 在Windows上建议使用Windows Terminal

2. **日志文件权限问题**
   - 确保 `logs/` 目录有写入权限
   - 检查磁盘空间是否充足

3. **Weights & Biases连接问题**
   - 确保网络连接正常
   - 检查API密钥配置：`wandb login`

## 性能影响 / Performance Impact

- Rich显示功能对训练性能影响极小（<1%）
- 日志记录开销可忽略不计
- W&B上传可能会轻微影响训练速度，可通过配置调整上传频率

## 未来改进 / Future Improvements

- [ ] 添加GPU使用率和内存监控
- [ ] 实现训练时间预估功能
- [ ] 添加模型收敛性可视化
- [ ] 支持分布式训练进度同步
- [ ] 集成TensorBoard支持
