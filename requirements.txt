# GRAG Project Dependencies / GRAG项目依赖
# Updated for Qwen3 Models Support / 已更新支持Qwen3模型
# Installation: pip install -r requirements.txt
# 安装方法: pip install -r requirements.txt

# ============================================================================
# Core ML/AI Libraries / 核心机器学习/AI库
# ============================================================================
torch>=2.0.0  # PyTorch深度学习框架
transformers>=4.51.0  # HuggingFace Transformers (Required for Qwen3 models / Qwen3模型必需)
datasets>=2.14.0  # HuggingFace数据集库
accelerate>=0.24.0  # 分布式训练加速库
peft>=0.6.0  # Parameter Efficient Fine-Tuning / 参数高效微调
trl>=0.7.0  # Transformer Reinforcement Learning / 强化学习库

# Note: unsloth requires separate installation due to build complexity
# 注意: unsloth需要单独安装，因为构建复杂性
# Install with: pip install "unsloth[cu121-torch240] @ git+https://github.com/unslothai/unsloth.git"

# ============================================================================
# LangGraph and LangChain Ecosystem / LangGraph和LangChain生态系统
# ============================================================================
langgraph>=0.2.0  # 工作流编排框架
langchain>=0.3.0  # LangChain核心库
langchain-community>=0.3.0  # 社区集成
langchain-openai>=0.2.0  # OpenAI集成
langchain-anthropic>=0.2.0  # Anthropic集成
langchain-text-splitters>=0.3.0  # 文本分割器
langchainhub>=0.1.15  # LangChain Hub

# ============================================================================
# Vector Database and Search / 向量数据库和搜索
# ============================================================================
# FAISS - Choose CPU or GPU version / FAISS - 选择CPU或GPU版本
faiss-cpu>=1.10.0  # CPU版本 (默认)
# faiss-gpu-cu12>=1.11.0  # GPU版本 CUDA 12.x (uncomment for GPU / 取消注释以使用GPU)
# faiss-gpu-cu11>=1.11.0  # GPU版本 CUDA 11.x (uncomment for GPU / 取消注释以使用GPU)

sentence-transformers>=2.7.0  # 句子嵌入库 (Required for Qwen3 / Qwen3必需)
chromadb>=0.4.0  # Chroma向量数据库



# ============================================================================
# Web Search and Scraping / 网络搜索和爬虫
# ============================================================================
tavily-python>=0.3.0  # Tavily搜索API
requests>=2.31.0  # HTTP请求库
beautifulsoup4>=4.12.0  # HTML解析库

# ============================================================================
# Data Processing / 数据处理
# ============================================================================
pandas>=2.0.0  # 数据分析库
numpy>=1.24.0  # 数值计算库
scikit-learn>=1.3.0  # 机器学习库
jieba>=0.42.1  # 中文分词库（注意：可能有pkg_resources警告）
opencc-python-reimplemented>=0.1.7  # 中文繁简转换
setuptools<81  # 固定setuptools版本以避免pkg_resources警告

# ============================================================================
# Evaluation / 评估
# ============================================================================
rouge-score>=0.1.2  # ROUGE评估指标
nltk>=3.8.0  # 自然语言处理工具包
bert-score>=0.3.13  # BERTScore评估指标
ragas>=0.1.0  # RAG评估框架

# ============================================================================
# Frontend / 前端
# ============================================================================
streamlit>=1.28.0  # Web应用框架
plotly>=5.17.0  # 交互式图表库
altair>=5.1.0  # 统计可视化库
streamlit-chat>=0.1.1  # Streamlit聊天组件
streamlit-option-menu>=0.3.6  # Streamlit选项菜单

# ============================================================================
# Utilities / 工具库
# ============================================================================
tqdm>=4.66.0  # 进度条库
wandb>=0.16.0  # 实验跟踪平台
python-dotenv>=1.0.0  # 环境变量管理
pyyaml>=6.0.1  # YAML解析库
loguru>=0.7.2  # 日志库
rich>=13.0.0  # 终端美化输出

# ============================================================================
# Medical NLP / 医疗自然语言处理
# ============================================================================
scispacy>=0.5.3  # 科学文本处理
# medspacy>=1.0.0  # 医疗NLP (may have encoding issues / 可能有编码问题)

# ============================================================================
# Development Tools / 开发工具
# ============================================================================
pytest>=7.4.0  # 测试框架
black>=23.9.0  # 代码格式化工具
flake8>=6.1.0  # 代码检查工具
pre-commit>=3.5.0  # Git钩子管理
jupyter>=1.0.0  # Jupyter笔记本

# ============================================================================
# Optional GPU Acceleration / 可选GPU加速
# ============================================================================
# flash-attn>=2.3.0  # Flash Attention (uncomment for GPU acceleration / 取消注释以启用GPU加速)
# vllm>=0.2.0  # vLLM推理引擎 (uncomment for faster inference / 取消注释以获得更快推理)

# ============================================================================
# Installation Notes / 安装说明
# ============================================================================
# 1. For GPU support, uncomment the appropriate faiss-gpu line above
#    GPU支持: 取消注释上面相应的faiss-gpu行
#
# 2. Run the installation helper script for automatic setup:
#    运行安装助手脚本进行自动设置:
#    python scripts/install_dependencies.py
#
# 3. For manual installation with mirrors (China users):
#    手动安装使用镜像源（中国用户）:
#    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
#
# 4. Some packages may require system dependencies:
#    某些包可能需要系统依赖:
#    - For scispacy: python -m spacy download en_core_web_sm
#    - For flash-attn: requires CUDA development tools
#
# 5. If you encounter build errors, try the safe installation script:
#    如果遇到构建错误，尝试安全安装脚本:
#    python scripts/install_dependencies.py
